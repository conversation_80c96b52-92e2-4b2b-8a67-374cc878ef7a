[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:pyAHC HDF5基础集成项目 DESCRIPTION:为pyAHC系统添加基础的HDF5数据存储支持，实现日循环模拟中的状态变量传递，为未来的数据同化功能提供基础架构

项目目标：
1. 实现基础的HDF5数据存储功能
2. 支持日循环模拟中的状态变量传递
3. 为数据同化预留简单的接口结构
4. 保持与现有pyAHC架构的兼容性

成功指标：
- 能够保存和加载模型状态变量
- 支持连续多日模拟的状态传递
- HDF5文件结构清晰合理
- 与现有代码无缝集成

预期时间：4-6周
项目复杂度：中等
--[ ] NAME:第一阶段：基础HDF5支持 DESCRIPTION:实现核心HDF5数据管理功能，包括ProjectHDF5Manager类、状态提取器和注入器，以及对象序列化支持

优先级：高（必须先完成）
估计时间：3-4周
依赖：无（基础模块）

主要交付物：
- pyahc/db/hdf5/模块结构
- ProjectHDF5Manager核心类
- StateExtractor和StateInjector类
- Model和Result类的HDF5集成
- 完整的单元测试套件

技术要求：
- 熟悉HDF5和h5py库
- 理解pyAHC模型架构
- Python面向对象编程
- 单元测试和数据序列化
---[ ] NAME:1.1 创建核心HDF5模块结构 DESCRIPTION:在pyahc/db/hdf5/目录下创建模块结构，包括__init__.py、manager.py、state_extractor.py、state_injector.py等文件

验收标准：
- 创建pyahc/db/hdf5/目录结构
- 实现__init__.py文件，导出主要类和函数
- 创建manager.py文件框架，包含ProjectHDF5Manager类定义
- 创建state_extractor.py文件框架，包含StateExtractor类定义
- 创建state_injector.py文件框架，包含StateInjector类定义
- 添加必要的依赖项（h5py, numpy, pickle等）
- 所有文件包含适当的文档字符串和类型注解
- 通过基本的导入测试
---[ ] NAME:1.2 实现ProjectHDF5Manager核心类 DESCRIPTION:实现ProjectHDF5Manager类，包括文件管理、项目创建、日数据保存/加载、对象序列化等核心功能

验收标准：
- 实现__init__, __enter__, __exit__方法，支持上下文管理
- 实现open()和close()方法，正确管理HDF5文件连接
- 实现create_project()方法，创建项目组和元数据结构
- 实现save_daily_data()方法，保存模型、结果和状态变量
- 实现load_daily_data()方法，加载完整的日数据
- 实现_save_model_object()和_load_model_object()方法
- 实现_save_result_object()和_load_result_object()方法
- 实现_save_state_parameter_variables()方法
- 实现_load_state_variables()和_load_parameter_variables()方法
- 支持数据压缩和元数据存储
- 包含完整的错误处理和日志记录
- 通过单元测试验证所有方法功能
---[ ] NAME:1.3 实现StateExtractor状态提取器 DESCRIPTION:实现StateExtractor类，从模型结果中提取土壤含水量、LAI、生物量、根深、地下水位等状态变量

验收标准：
- 实现extract_all_states()静态方法，提取所有状态变量
- 实现extract_soil_moisture()方法，从Result对象提取土壤含水量剖面
- 实现extract_crop_lai()方法，提取叶面积指数
- 实现extract_crop_biomass()方法，提取作物生物量
- 实现extract_root_depth()方法，提取根深信息
- 实现extract_groundwater_level()方法，提取地下水位
- 支持从CSV和ASCII输出格式中提取数据
- 实现_parse_soil_moisture_from_sba()等辅助方法
- 包含健壮的错误处理和数据验证
- 返回适当的numpy数组或标量值
- 通过测试用例验证提取精度和可靠性
---[ ] NAME:1.4 实现StateInjector状态注入器 DESCRIPTION:实现StateInjector类，将状态变量注入到模型输入中，支持土壤含水量、作物状态、地下水位等注入

验收标准：
- 实现inject_all_states()静态方法，注入所有状态变量
- 实现inject_soil_moisture()方法，将土壤含水量注入模型
- 实现inject_crop_state()方法，注入作物LAI、生物量、根深
- 实现inject_groundwater_level()方法，注入地下水位
- 确保注入过程不破坏原始模型对象（使用深拷贝）
- 支持部分状态变量注入（可选参数）
- 实现数据类型转换和验证
- 包含完整的错误处理和回滚机制
- 与pyAHC模型组件结构兼容
- 通过测试验证注入后模型的完整性和正确性
---[ ] NAME:1.5 集成到现有Model和Result类 DESCRIPTION:在现有的Model和Result类中添加HDF5支持方法，包括save_to_project_hdf5、load_from_project_hdf5等方法

验收标准：
- 在Model类中添加save_to_project_hdf5()方法
- 在Model类中添加load_from_project_hdf5()类方法
- 在Model类中添加extract_current_states()方法
- 在Model类中添加extract_parameters()方法
- 在Result类中添加extract_states_for_hdf5()方法
- 在Result类中添加get_soil_moisture_profile()等便捷方法
- 为关键组件类添加to_state_dict()和from_state_dict()方法
- 确保方法与现有架构无缝集成
- 保持向后兼容性
- 添加适当的类型注解和文档字符串
- 通过集成测试验证与HDF5管理器的协作
---[ ] NAME:1.6 编写基础单元测试 DESCRIPTION:为所有核心类和方法编写单元测试，确保基础功能的正确性和稳定性

验收标准：
- 为ProjectHDF5Manager类编写完整的单元测试
- 为StateExtractor类的所有方法编写测试用例
- 为StateInjector类的所有方法编写测试用例
- 测试覆盖率达到90%以上
- 包含正常情况和异常情况的测试
- 测试数据序列化和反序列化的完整性
- 测试HDF5文件结构的正确性
- 使用模拟数据和真实数据进行测试
- 包含性能基准测试
- 所有测试能够独立运行且可重复
- 测试文档清晰，包含测试目的和预期结果
--[ ] NAME:第二阶段：完整工作流程 DESCRIPTION:实现项目级日循环模拟工作流程，添加项目汇总和时间序列管理功能

优先级：高（核心功能）
估计时间：2-3周
依赖：第一阶段完成

主要交付物：
- 项目初始化功能
- 日循环模拟流程
- 项目汇总和统计功能
- 完整的项目模拟流程
- 数据验证和错误处理
- 集成测试和使用示例

技术要求：
- 理解数据同化流程
- 时间序列数据处理
- 错误处理和日志管理
- 数据统计和分析
---[ ] NAME:2.1 实现项目初始化功能 DESCRIPTION:实现initialize_project_simulation函数，支持项目元数据配置、HDF5文件创建和项目结构初始化

验收标准：
- 实现完整的initialize_project_simulation()函数
- 支持配置字典输入，包含所有必要参数
- 创建ProjectHDF5Manager实例并初始化HDF5文件
- 生成结构化的项目元数据（model_info, simulation_period, location_info, crop_info）
- 调用create_project()方法创建项目结构
- 包含参数验证和默认值处理
- 支持日期格式转换和验证
- 返回初始化完成的ProjectHDF5Manager实例
- 包含完整的错误处理和日志记录
- 通过测试验证初始化后的项目结构正确性
---[ ] NAME:2.2 实现日循环模拟流程 DESCRIPTION:实现daily_simulation_step_v2函数，支持单日模拟、状态传递、模型运行和结果保存

验收标准：
- 实现完整的daily_simulation_step_v2()函数
- 支持从基础模型创建当日模型实例
- 实现从前一日加载状态变量的逻辑
- 使用StateInjector将状态注入当日模型
- 正确更新模拟日期（tstart和tend）
- 调用模型运行并处理可能的异常
- 使用StateExtractor提取当日状态变量
- 实现extract_parameters_from_model()辅助函数
- 调用save_daily_data()保存所有数据
- 返回模型运行结果
- 包含完整的错误处理和日志记录
- 通过测试验证状态传递的正确性
---[ ] NAME:2.3 实现项目汇总功能 DESCRIPTION:实现generate_project_summary函数，生成时间序列数据、统计信息和项目级别汇总报告

验收标准：
- 实现完整的generate_project_summary()函数
- 创建summary组和timeseries、statistics子组
- 收集整个模拟期间的LAI、生物量、土壤含水量数据
- 生成并保存lai_timeseries、biomass_timeseries、moisture_timeseries数据集
- 为每个时间序列数据集添加单位和日期元数据
- 计算并保存统计信息（最大值、平均值、最小值等）
- 支持数据压缩和缺失值处理
- 处理不完整数据和异常情况
- 生成有意义的项目级别汇总信息
- 包含适当的日志记录和进度提示
- 通过测试验证汇总数据的准确性和完整性
---[ ] NAME:2.4 实现完整项目模拟流程 DESCRIPTION:实现run_project_simulation函数，整合初始化、日循环模拟和汇总功能，实现完整的项目模拟流程

验收标准：
- 实现完整的run_project_simulation()函数
- 调用initialize_project_simulation()初始化项目
- 创建基础模型实例（使用现有函数）
- 实现日循环模拟逻辑，调用daily_simulation_step_v2()
- 支持可选的数据同化功能（预留接口）
- 实现进度跟踪和日志记录
- 包含健墮的错误处理和恢复机制
- 支持stop_on_error配置选项
- 调用generate_project_summary()生成汇总报告
- 返回初始化完成的ProjectHDF5Manager实例
- 支持上下文管理和资源清理
- 通过集成测试验证完整流程的正确性
---[ ] NAME:2.5 添加数据验证和错误处理 DESCRIPTION:实现健壮的数据验证和错误处理机制，包括数据一致性检查、异常恢复和日志记录
---[ ] NAME:2.6 编写集成测试和示例 DESCRIPTION:编写完整的集成测试和使用示例，验证完整工作流程的正确性和性能
--[ ] NAME:第三阶段：数据同化准备 DESCRIPTION:设计和实现数据同化接口，为后续集成观测数据同化功能提供基础架构

优先级：中（预留功能）
估计时间：2-3周
依赖：第二阶段完成

主要交付物：
- 数据同化接口架构设计
- 观测数据处理模块
- 同化变量存储功能
- 集合模拟支持预留
- 数据同化测试框架
- 性能优化和大规模测试

技术要求：
- 数据同化理论基础
- 观测数据处理经验
- 不确定性量化方法
- 系统架构设计
---[ ] NAME:3.1 设计数据同化接口架构 DESCRIPTION:设计数据同化接口架构，包括AssimilationManager类、观测数据处理和同化变量存储结构

验收标准：
- 设计完整的AssimilationManager类架构
- 定义观测数据的标准格式和接口
- 设计同化变量的HDF5存储结构
- 定义数据同化的核心接口方法
- 设计不同同化算法的插件架构
- 定义观测数据质量控制标准
- 设计不确定性量化和传播机制
- 定义同化结果的验证和评估标准
- 创建详细的技术设计文档
- 定义清晰的API接口和数据流
- 考虑未来扩展性和向后兼容性
---[ ] NAME:3.2 实现观测数据处理模块 DESCRIPTION:实现观测数据处理模块，实现观测数据加载、验证和预处理功能，支持多种观测数据格式和质量控制

验收标准：
- 实现ObservationDataLoader类，支持多种数据格式
- 支持CSV、JSON、NetCDF等常见观测数据格式
- 实现数据验证和质量控制功能
- 支持时间匹配和空间插值
- 实现观测误差估计和不确定性量化
- 支持缺失数据处理和异常值检测
- 实现数据预处理和标准化功能
- 支持多种观测变量（LAI、土壤含水量、生物量等）
- 实现观测数据的元数据管理
- 包含完整的错误处理和日志记录
- 通过测试验证数据加载和处理的正确性
---[ ] NAME:3.3 实现同化变量存储功能 DESCRIPTION:实现assimilate_observations_v2函数，支持同化后状态变量的存储、版本管理和元数据记录

验收标准：
- 实现完整的assimilate_observations_v2()函数
- 支持加载当前日数据和状态变量
- 实现同化算法的插件接口（占位符实现）
- 支持同化后变量的HDF5存储
- 为同化变量添加丰富的元数据（同化日期、原始变量、算法信息）
- 实现同化结果的版本管理和历史记录
- 支持部分变量同化和选择性更新
- 实现同化质量评估和统计信息
- 支持不同同化算法的结果对比
- 包含完整的错误处理和回滚机制
- 通过测试验证同化数据存储的正确性和完整性
---[ ] NAME:3.4 预留集合模拟支持 DESCRIPTION:为后续的集合模拟功能预留接口和数据结构，支持多成员集合和不确定性量化

验收标准：
- 设计集合模拟的HDF5数据结构
- 定义EnsembleManager类的接口和方法
- 设计多成员集合的存储和管理机制
- 定义集合统计和不确定性量化方法
- 设计集合成员的初始化和扰动机制
- 定义集合同化的数据流和接口
- 设计集合预报和不确定性传播机制
- 定义集合结果的分析和可视化接口
- 考虑并行计算和分布式处理需求
- 创建详细的技术设计文档和实现计划
- 实现基本的框架代码和接口定义
---[ ] NAME:3.5 实现数据同化测试框架 DESCRIPTION:实现数据同化测试框架，包括模拟观测数据生成、同化算法测试和结果验证
---[ ] NAME:3.6 性能优化和大规模测试 DESCRIPTION:进行性能优化和大规模数据测试，确保系统在长期模拟和大量数据情况下的稳定性
--[ ] NAME:第四阶段：高级功能 DESCRIPTION:实现多项目管理、数据导出、可视化工具和性能优化功能

优先级：低（增强功能）
估计时间：3-4周
依赖：第三阶段完成

主要交付物：
- 多项目管理系统
- 数据导出工具和模板
- 可视化工具和仪表板
- 并行计算支持
- 云存储适配
- 完整文档和教程

技术要求：
- Web开发和用户界面设计
- 数据可视化库（matplotlib, plotly等）
- 并行计算和分布式系统
- 云平台和存储服务
- 技术文档编写
---[ ] NAME:4.1 实现多项目管理功能 DESCRIPTION:实现多项目管理功能，包括项目列表、比较分析、批量操作和项目间数据共享

验收标准：
- 实现MultiProjectManager类，管理多个项目
- 实现list_projects()方法，列出所有项目和元数据
- 实现compare_projects()方法，比较不同项目的结果
- 实现batch_operations()支持批量处理和分析
- 支持项目间数据共享和复制
- 实现项目统计信息的汇总和对比
- 支持项目的归档和备份功能
- 实现项目权限管理和访问控制
- 支持项目模板和快速创建
- 实现项目搜索和过滤功能
- 包含完整的用户界面和命令行工具
- 通过测试验证多项目管理的正确性和效率
---[ ] NAME:4.2 实现数据导出工具 DESCRIPTION:实现数据导出工具，支持将HDF5数据导出为CSV、NetCDF、JSON等格式，以及自定义导出模板

验收标准：
- 实现DataExporter类，支持多种导出格式
- 支持导出为CSV格式，包含时间序列和统计数据
- 支持导出为NetCDF格式，保持元数据和结构
- 支持导出为JSON格式，适合Web应用和API
- 实现自定义导出模板和配置系统
- 支持选择性数据导出（日期范围、变量类型）
- 实现数据转换和格式化功能
- 支持大数据集的分块导出和流式处理
- 实现导出进度跟踪和日志记录
- 支持导出数据的验证和质量检查
- 包含命令行工具和程序化接口
- 通过测试验证导出数据的准确性和完整性
---[ ] NAME:4.3 实现可视化工具 DESCRIPTION:实现可视化工具，包括时间序列图表、空间分布图、统计分析图和交互式仪表板

验收标准：
- 实现VisualizationManager类，管理所有可视化功能
- 实现时间序列图表（LAI、生物量、土壤含水量等）
- 实现空间分布图（土壤剖面、热力图等）
- 实现统计分析图（直方图、相关性分析等）
- 实现交互式仪表板，集成多种图表和控件
- 支持多项目比较和叠加显示
- 实现图表的保存和导出功能
- 支持不同的图表样式和主题
- 实现数据的实时更新和动态显示
- 支持Web界面和桌面应用两种模式
- 包含用户友好的交互界面和帮助文档
- 通过测试验证可视化结果的准确性和美观性
---[ ] NAME:4.4 实现并行计算支持 DESCRIPTION:实现并行计算支持，包括多进程模拟、分布式计算和任务调度管理
---[ ] NAME:4.5 实现云存储适配 DESCRIPTION:实现云存储适配功能，支持AWS S3、Azure Blob、Google Cloud Storage等云存储服务
---[ ] NAME:4.6 编写完整文档和教程 DESCRIPTION:编写完整的用户文档、API文档、教程和最佳实践指南，包括代码示例和案例研究

验收标准：
- 编写完整的用户手册，包括安装、配置和使用指南
- 编写详细的API文档，包括所有类和方法的说明
- 创建分步骤教程，从基础到高级功能
- 编写最佳实践指南，包括性能优化和故障排除
- 提供丰富的代码示例和使用场景
- 创建完整的案例研究，展示实际应用
- 实现交互式文档网站和在线帮助
- 提供视频教程和网络研讨会资料
- 建立用户社区和问答平台
- 定期更新文档和维护示例代码
- 包含多语言支持（中英文）
- 通过用户反馈验证文档的清晰性和实用性